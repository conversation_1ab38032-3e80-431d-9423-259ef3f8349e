import Store from "electron-store"

interface StoreSchema {
  // Store schema for app preferences - API key storage removed as we now use Clerk auth
  windowPosition?: { x: number; y: number }
  windowSize?: { width: number; height: number }
}

const store = new Store<StoreSchema>({
  defaults: {
    // No defaults needed for now
  },
  encryptionKey: "your-encryption-key"
}) as Store<StoreSchema> & {
  store: StoreSchema
  get: <K extends keyof StoreSchema>(key: K) => StoreSchema[K]
  set: <K extends keyof StoreSchema>(key: K, value: StoreSchema[K]) => void
}

export { store }
