#!/bin/bash

# Exit on error
set -e

# Display what's happening
echo "Building Interview Coder application..."

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

# Clean up any previous builds
echo "Cleaning up previous builds..."
npm run clean

# Install dependencies if needed
if [ ! -d "node_modules" ] || [ ! -d "node_modules/electron" ]; then
    echo "Installing dependencies..."
    npm install
else
    echo "Dependencies already installed. Checking for updates..."
    npm update
fi

# Rebuild Electron
echo "Rebuilding Electron..."
npm rebuild electron

# Build the application
echo "Building the application..."
npm run build

echo "Build completed successfully!"
