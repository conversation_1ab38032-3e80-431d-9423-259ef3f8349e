import { useRef, useEffect } from "react"
import {
  SignedIn,
  SignedOut,
  SignIn<PERSON>utton,
  UserButton,
} from "@clerk/clerk-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "./ui/card"
import { Button } from "./ui/button"

const ClerkAuth: React.FC = () => {
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Height update logic for Electron window sizing
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  return (
    <div
      ref={contentRef}
      className="w-fit h-fit flex flex-col items-center justify-center bg-gray-50 rounded-xl"
    >
      <SignedOut>
        <Card>
          <CardHeader className="space-y-2">
            <CardTitle className="text-2xl font-semibold text-center">
              Welcome to Interview Coder
            </CardTitle>
            <CardDescription className="text-center text-gray-500">
              Please sign in with your Google account to continue. 
              Press Cmd + B to hide/show the window.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <SignInButton mode="modal">
                <Button className="w-full font-medium">
                  Sign in with Google
                </Button>
              </SignInButton>
            </div>
          </CardContent>
        </Card>
      </SignedOut>
      
      <SignedIn>
        <Card>
          <CardHeader className="space-y-2">
            <CardTitle className="text-2xl font-semibold text-center">
              Welcome Back!
            </CardTitle>
            <CardDescription className="text-center text-gray-500">
              You are successfully signed in.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <UserButton afterSignOutUrl="/" />
          </CardContent>
        </Card>
      </SignedIn>
    </div>
  )
}

export default ClerkAuth
